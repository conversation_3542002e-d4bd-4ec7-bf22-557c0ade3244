import { Button } from '@/components/ui/button'
import { IconPlus, IconFileImport, IconFileExport } from '@tabler/icons-react'
import { useTimesheet } from '../context/timesheet-context'
import { useCommonTranslations } from '@/context/i18n-context'

export function WorkTimePeriodPrimaryButtons() {
  const { setOpen } = useTimesheet()
  const { t } = useCommonTranslations()

  return (
    <div className='flex flex-wrap items-center gap-2'>
      <Button onClick={() => setOpen('create-period')}>
        <IconPlus className='mr-2 h-4 w-4' />
        {t('buttons.add_work_period')}
      </Button>

      <Button variant='outline' onClick={() => setOpen('import')}>
        <IconFileImport className='mr-2 h-4 w-4' />
        {t('buttons.import')}
      </Button>

      <Button variant='outline'>
        <IconFileExport className='mr-2 h-4 w-4' />
        {t('buttons.export')}
      </Button>
    </div>
  )
}
