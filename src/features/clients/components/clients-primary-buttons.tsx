import { IconPlus, IconFileImport } from '@tabler/icons-react'
import { But<PERSON> } from '@/components/ui/button'
import { useClients } from '../context/clients-context'
import { useCommonTranslations } from '@/context/i18n-context'

export function ClientsPrimaryButtons() {
  const { setOpen } = useClients()
  const { t } = useCommonTranslations()

  return (
    <div className='flex flex-wrap items-center gap-2'>
      <Button
        variant='outline'
        size='sm'
        className='ml-auto h-8 lg:flex'
        onClick={() => setOpen('create')}
      >
        <IconFileImport className='mr-2 h-4 w-4' />
        {t('buttons.import_clients')}
      </Button>
      <Button
        size='sm'
        className='h-8'
        onClick={() => setOpen('create')}
      >
        <IconPlus className='mr-2 h-4 w-4' />
        {t('buttons.add_client')}
      </Button>
    </div>
  )
}
