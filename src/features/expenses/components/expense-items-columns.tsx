import { ColumnDef } from '@tanstack/react-table'
import { useNavigate } from '@tanstack/react-router'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { IconDots, IconEdit, IconTrash, IconEye, IconReceipt } from '@tabler/icons-react'
import { ExpenseItem } from '../data/schema'
import { useExpenses } from '../context/expenses-context'
import { useCommonTranslations } from '@/context/i18n-context'
import { format } from 'date-fns'
import { useDateLocale } from '@/lib/date-locale'

const statusColors = {
  draft: 'secondary',
  submitted: 'default',
  approved: 'default',
  rejected: 'destructive',
} as const

export function useExpenseItemsColumns(): ColumnDef<ExpenseItem>[] {
  const { t } = useCommonTranslations()
  const locale = useDateLocale()

  const statusLabels = {
    draft: t('expense_status.draft'),
    submitted: t('expense_status.submitted'),
    approved: t('expense_status.approved'),
    rejected: t('expense_status.rejected'),
  }

  return [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label={t('table.actions.select_all')}
        className='translate-y-[2px]'
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label={t('table.actions.select_row')}
        className='translate-y-[2px]'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'title',
    header: t('table.columns.title'),
    cell: ({ row }) => (
      <div className='font-medium max-w-[200px] truncate'>
        {row.getValue('title')}
      </div>
    ),
  },
  {
    accessorKey: 'client',
    header: t('table.columns.client'),
    cell: ({ row }) => (
      <div className='font-medium'>{row.getValue('client')}</div>
    ),
  },
  {
    accessorKey: 'startDate',
    header: t('table.columns.period'),
    cell: ({ row }) => {
      const startDate = new Date(row.getValue('startDate'))
      const endDate = new Date(row.original.endDate)
      const isSameDay = startDate.toDateString() === endDate.toDateString()

      return (
        <div className='text-sm'>
          {isSameDay ? (
            format(startDate, 'MMM d, yyyy', { locale })
          ) : (
            <>
              {format(startDate, 'MMM d', { locale })} - {format(endDate, 'MMM d, yyyy', { locale })}
            </>
          )}
        </div>
      )
    },
  },
  {
    accessorKey: 'totalAmount',
    header: t('table.columns.total_amount'),
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue('totalAmount'))
      return (
        <div className='font-medium'>
          {amount.toLocaleString('de-CH', {
            style: 'currency',
            currency: 'CHF',
          })}
        </div>
      )
    },
  },
  {
    accessorKey: 'expenseCount',
    header: t('table.columns.expenses'),
    cell: ({ row }) => {
      const count = row.getValue('expenseCount') as number
      return (
        <div className='flex items-center space-x-1'>
          <IconReceipt className='h-4 w-4 text-muted-foreground' />
          <span>{count}</span>
        </div>
      )
    },
  },
  {
    accessorKey: 'status',
    header: t('table.columns.status'),
    cell: ({ row }) => {
      const status = row.getValue('status') as keyof typeof statusColors
      return (
        <Badge variant={statusColors[status]}>
          {statusLabels[status]}
        </Badge>
      )
    },
  },
  {
    id: 'actions',
    enableHiding: false,
    cell: ({ row }) => {
      const expenseItem = row.original
      // eslint-disable-next-line react-hooks/rules-of-hooks
      const { setOpen, setCurrentItem } = useExpenses()
      // eslint-disable-next-line react-hooks/rules-of-hooks
      const navigate = useNavigate()

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='ghost' className='h-8 w-8 p-0'>
              <span className='sr-only'>{t('aria_labels.open_menu')}</span>
              <IconDots className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuLabel>{t('table.columns.actions')}</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => {
                navigate({ to: '/expenses/$itemId', params: { itemId: expenseItem.id } })
              }}
            >
              <IconEye className='mr-2 h-4 w-4' />
              {t('table.actions.view')}
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => {
                setCurrentItem(expenseItem)
                setOpen('update-item')
              }}
            >
              <IconEdit className='mr-2 h-4 w-4' />
              {t('table.actions.edit')}
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => {
                setCurrentItem(expenseItem)
                setOpen('delete-item')
              }}
              className='text-destructive'
            >
              <IconTrash className='mr-2 h-4 w-4' />
              {t('table.actions.delete')}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
  ]
}
