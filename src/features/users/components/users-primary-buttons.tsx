import { IconMailPlus, IconUserPlus } from '@tabler/icons-react'
import { But<PERSON> } from '@/components/ui/button'
import { useUsers } from '../context/users-context'
import { useCommonTranslations } from '@/context/i18n-context'

export function UsersPrimaryButtons() {
  const { setOpen } = useUsers()
  const { t } = useCommonTranslations()

  return (
    <div className='flex gap-2'>
      <Button
        variant='outline'
        className='space-x-1'
        onClick={() => setOpen('invite')}
      >
        <span>{t('buttons.invite_user')}</span> <IconMailPlus size={18} />
      </Button>
      <Button className='space-x-1' onClick={() => setOpen('add')}>
        <span>{t('buttons.add_user')}</span> <IconUserPlus size={18} />
      </Button>
    </div>
  )
}
