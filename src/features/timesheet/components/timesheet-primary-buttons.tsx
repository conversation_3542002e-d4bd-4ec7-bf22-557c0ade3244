import { IconDownload, IconPlus } from '@tabler/icons-react'
import { But<PERSON> } from '@/components/ui/button'
import { useTimesheet } from '../context/timesheet-context'
import { useCommonTranslations } from '@/context/i18n-context'

export function TimesheetPrimaryButtons() {
  const { setOpen } = useTimesheet()
  const { t } = useCommonTranslations()

  return (
    <div className='flex gap-2'>
      <Button
        variant='outline'
        className='space-x-1'
        onClick={() => setOpen('import')}
      >
        <span>{t('buttons.import')}</span> <IconDownload size={18} />
      </Button>
      <Button className='space-x-1' onClick={() => setOpen('create')}>
        <span>{t('buttons.add_entry')}</span> <IconPlus size={18} />
      </Button>
    </div>
  )
}
