{"navigation": {"dashboard": "Dashboard", "mydashboard": "My Dashboard", "timesheet": "Timesheet", "expenses": "Expenses", "non-working-days": "Non-Working Days", "clients": "Clients", "consultants": "Consultants", "users": "Users", "settings": "Settings", "apps": "Apps", "chats": "Chats", "tasks": "Tasks", "help-center": "Help Center", "general": "General", "management": "Management", "other": "Other"}, "buttons": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "create": "Create", "update": "Update", "submit": "Submit", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "confirm": "Confirm", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "refresh": "Refresh", "view": "View", "download": "Download", "upload": "Upload", "validate": "Validate"}, "actions": {"loading": "Loading...", "saving": "Saving...", "deleting": "Deleting...", "updating": "Updating...", "creating": "Creating...", "processing": "Processing...", "uploading": "Uploading...", "downloading": "Downloading..."}, "status": {"pending": "Pending", "approved": "Approved", "rejected": "Rejected", "validated": "Validated", "draft": "Draft", "submitted": "Submitted"}, "common": {"name": "Name", "email": "Email", "phone": "Phone", "address": "Address", "date": "Date", "time": "Time", "description": "Description", "notes": "Notes", "comments": "Comments", "total": "Total", "amount": "Amount", "quantity": "Quantity", "price": "Price", "category": "Category", "type": "Type", "status": "Status", "priority": "Priority", "tags": "Tags", "file": "File", "files": "Files", "image": "Image", "images": "Images", "document": "Document", "documents": "Documents", "attachment": "Attachment", "attachments": "Attachments", "language": "Language", "theme": "Theme", "profile": "Profile", "account": "Account", "password": "Password", "username": "Username", "role": "Role", "permissions": "Permissions", "settings": "Settings", "preferences": "Preferences", "notifications": "Notifications", "appearance": "Appearance", "display": "Display", "help": "Help", "support": "Support", "about": "About", "version": "Version", "copyright": "Copyright", "terms": "Terms", "privacy": "Privacy", "contact": "Contact", "feedback": "<PERSON><PERSON><PERSON>", "report": "Report", "analytics": "Analytics", "statistics": "Statistics", "overview": "Overview", "summary": "Summary", "details": "Details", "history": "History", "logs": "Logs", "activity": "Activity", "recent": "Recent", "new": "New", "old": "Old", "latest": "Latest", "first": "First", "last": "Last", "all": "All", "none": "None", "other": "Other", "unknown": "Unknown", "yes": "Yes", "no": "No", "true": "True", "false": "False", "enabled": "Enabled", "disabled": "Disabled", "public": "Public", "private": "Private", "shared": "Shared", "personal": "Personal", "business": "Business", "work": "Work", "home": "Home", "mobile": "Mobile", "office": "Office", "online": "Online", "offline": "Offline", "available": "Available", "unavailable": "Unavailable", "busy": "Busy", "free": "Free", "admin": "Admin", "consultant": "Consultant"}, "messages": {"success": "Operation successful", "error": "An error occurred", "warning": "Warning", "info": "Information", "confirm_delete": "Are you sure you want to delete this item?", "no_data": "No data available", "no_results": "No results found", "loading": "Loading...", "please_wait": "Please wait...", "try_again": "Please try again", "something_went_wrong": "Something went wrong", "network_error": "Network error", "server_error": "Server error", "unauthorized": "Unauthorized", "forbidden": "Forbidden", "not_found": "Not found", "validation_error": "Validation error", "required_field": "This field is required", "invalid_format": "Invalid format", "invalid_email": "Invalid email address", "password_too_short": "Password is too short", "passwords_dont_match": "Passwords don't match", "session_expired": "Session expired", "login_required": "Login required", "access_denied": "Access denied", "operation_cancelled": "Operation cancelled", "changes_saved": "Changes saved", "changes_discarded": "Changes discarded", "unsaved_changes": "Unsaved changes", "are_you_sure": "Are you sure?", "cannot_undo": "This action cannot be undone"}, "time": {"now": "Now", "today": "Today", "yesterday": "Yesterday", "tomorrow": "Tomorrow", "this_week": "This week", "last_week": "Last week", "next_week": "Next week", "this_month": "This month", "last_month": "Last month", "next_month": "Next month", "this_year": "This year", "last_year": "Last year", "next_year": "Next year", "minutes_ago": "{{count}} minute ago", "minutes_ago_plural": "{{count}} minutes ago", "hours_ago": "{{count}} hour ago", "hours_ago_plural": "{{count}} hours ago", "days_ago": "{{count}} day ago", "days_ago_plural": "{{count}} days ago", "weeks_ago": "{{count}} week ago", "weeks_ago_plural": "{{count}} weeks ago", "months_ago": "{{count}} month ago", "years_ago": "{{count}} year ago", "years_ago_plural": "{{count}} years ago", "day": "day", "days": "days", "hour": "hour", "hours": "hours", "minute": "minute", "minutes": "minutes", "same_day": "Same day"}, "search": {"placeholder": "Search...", "no_results": "No results found", "results_count": "{{count}} result found", "results_count_plural": "{{count}} results found", "search_in": "Search in {{context}}", "clear_search": "Clear search", "advanced_search": "Advanced search", "filter_by": "Filter by", "sort_by": "Sort by", "order": "Order", "ascending": "Ascending", "descending": "Descending", "of": "of", "page": "Page", "rows_per_page": "Rows per page", "rows_selected": "row(s) selected", "go_to_first_page": "Go to first page", "go_to_previous_page": "Go to previous page", "go_to_next_page": "Go to next page", "go_to_last_page": "Go to last page", "toggle_columns": "Toggle columns", "selected": "selected", "clear_filters": "Clear filters"}, "pagination": {"page": "Page", "of": "of", "items_per_page": "Items per page", "showing": "Showing", "to": "to", "of_total": "of {{total}}", "first_page": "First page", "last_page": "Last page", "next_page": "Next page", "previous_page": "Previous page", "go_to_page": "Go to page"}, "theme": {"light": "Light", "dark": "Dark", "system": "System", "auto": "Auto", "theme": "Theme"}, "table": {"columns": {"title": "Title", "name": "Name", "client_name": "Client Name", "code": "Code", "description": "Description", "status": "Status", "date": "Date", "start_date": "Start Date", "end_date": "End Date", "hours": "Hours", "total_hours": "Total Hours", "location": "Location", "client": "Client", "project": "Project", "amount": "Amount", "total_amount": "Total Amount", "expenses": "Expenses", "period": "Period", "username": "Username", "email": "Email", "phone_number": "Phone Number", "full_name": "Full Name", "first_name": "First Name", "last_name": "Last Name", "role": "Role", "created_at": "Created At", "updated_at": "Updated At", "actions": "Actions"}, "actions": {"edit": "Edit", "delete": "Delete", "view": "View", "select_all": "Select all", "select_row": "Select row"}, "filters": {"filter_by_title": "Filter by title...", "filter_users": "Filter users...", "filter_clients": "Filter clients...", "filter_consultants": "Filter consultants...", "status": "Status", "client": "Client", "location": "Location"}, "empty_states": {"no_description": "No description", "no_project": "No project", "no_data": "No data available", "no_results": "No results found", "no_expense_items": "No expense items found"}}, "locations": {"office": "Office", "home": "Home", "remote": "Remote", "onsite": "Onsite"}, "expense_status": {"draft": "Draft", "submitted": "Submitted", "approved": "Approved", "rejected": "Rejected"}, "leave_status": {"pending": "Pending", "approved": "Approved", "rejected": "Rejected"}, "user_status": {"active": "Active", "inactive": "Inactive", "invited": "Invited", "suspended": "Suspended"}, "task_status": {"backlog": "Backlog", "todo": "Todo", "in_progress": "In Progress", "done": "Done", "canceled": "Canceled"}, "task_labels": {"bug": "Bug", "feature": "Feature", "documentation": "Documentation"}, "validation": {"required": "This field is required", "invalid_email": "Invalid email address", "invalid_format": "Invalid format", "min_length": "Minimum {{count}} characters required", "max_length": "Maximum {{count}} characters allowed", "positive_number": "Must be a positive number", "select_option": "Please select an option"}, "aria_labels": {"close": "Close", "open_menu": "Open menu", "sort_ascending": "Sort ascending", "sort_descending": "Sort descending", "hide_column": "Hide column", "change_language": "Change language", "toggle_theme": "Toggle theme"}, "skip_to_main": "Skip to Main"}