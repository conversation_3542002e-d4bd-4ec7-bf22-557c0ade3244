import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from '@/hooks/use-toast'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet'
import { SelectDropdown } from '@/components/select-dropdown'
import { DatePicker } from '@/components/ui/date-picker'
import { Label } from '@/components/ui/label'
import { IconUpload, IconX } from '@tabler/icons-react'
import { expenseFormSchema, ExpenseForm, Expense } from '../data/schema'
import { currencies, expenseCategories } from '../data/mock-data'
import { useTranslations } from '@/context/i18n-context'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentExpense?: Expense
  itemId?: string
}

export function ExpenseMutateDrawer({ open, onOpenChange, currentExpense, itemId }: Props) {
  const { t } = useTranslations('expenses')
  const isUpdate = !!currentExpense
  const [convertedAmount, setConvertedAmount] = useState<number | null>(null)
  const [exchangeRate, setExchangeRate] = useState<number | null>(null)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [loading, setLoading] = useState(false)

  const form = useForm<ExpenseForm>({
    resolver: zodResolver(expenseFormSchema),
    defaultValues: currentExpense ? {
      designation: currentExpense.designation,
      amount: currentExpense.amount.toString(),
      currency: currentExpense.currency,
      date: currentExpense.date,
      category: currentExpense.category,
      comment: currentExpense.comment || '',
    } : {
      designation: '',
      amount: '',
      currency: 'CHF',
      date: new Date().toISOString().split('T')[0],
      category: 'other',
      comment: '',
    },
  })

  const watchedCurrency = form.watch('currency')
  const watchedAmount = form.watch('amount')

  // Currency conversion effect
  useEffect(() => {
    const convertCurrency = async () => {
      if (watchedCurrency !== 'CHF' && watchedAmount && parseFloat(watchedAmount) > 0) {
        setLoading(true)
        try {
          const response = await fetch(
            `https://api.frankfurter.app/latest?amount=${watchedAmount}&from=${watchedCurrency}&to=CHF`
          )
          const data = await response.json()
          setConvertedAmount(data.rates.CHF)
          setExchangeRate(data.rates.CHF / parseFloat(watchedAmount))
        } catch (error) {
          console.error('Currency conversion error:', error)
          setConvertedAmount(null)
          setExchangeRate(null)
        } finally {
          setLoading(false)
        }
      } else {
        setConvertedAmount(null)
        setExchangeRate(null)
      }
    }

    const timeoutId = setTimeout(convertCurrency, 500) // Debounce API calls
    return () => clearTimeout(timeoutId)
  }, [watchedCurrency, watchedAmount])

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: t('form.file_too_large'),
          description: t('form.file_size_limit'),
          variant: 'destructive',
        })
        return
      }
      setSelectedFile(file)
    }
  }

  const removeFile = () => {
    setSelectedFile(null)
    const fileInput = document.getElementById('receipt-upload') as HTMLInputElement
    if (fileInput) fileInput.value = ''
  }

  const onSubmit = (data: ExpenseForm) => {
    // TODO: Implement actual API call
    console.log('Submitting expense:', {
      ...data,
      amount: parseFloat(data.amount),
      amountCHF: convertedAmount,
      exchangeRate,
      receipt: selectedFile,
      itemId,
    })

    onOpenChange(false)
    form.reset()
    setSelectedFile(null)
    setConvertedAmount(null)
    setExchangeRate(null)

    toast({
      title: isUpdate ? t('messages.expense_updated') : t('messages.expense_created'),
      description: `${t('messages.successfully')} ${isUpdate ? t('messages.updated') : t('messages.created')} ${t('messages.expense')} "${data.designation}".`,
    })
  }

  return (
    <Sheet
      open={open}
      onOpenChange={(v) => {
        onOpenChange(v)
        if (!v) {
          form.reset()
          setSelectedFile(null)
          setConvertedAmount(null)
          setExchangeRate(null)
        }
      }}
    >
      <SheetContent className='flex flex-col'>
        <SheetHeader className='text-left'>
          <SheetTitle>{isUpdate ? t('form.update_expense') : t('form.add_expense')}</SheetTitle>
          <SheetDescription>
            {isUpdate
              ? t('form.update_expense_description')
              : t('form.add_expense_description')}
            {' '}{t('form.click_save_when_done')}
          </SheetDescription>
        </SheetHeader>

        <Form {...form}>
          <form
            id='expense-form'
            onSubmit={form.handleSubmit(onSubmit)}
            className='flex-1 space-y-6 overflow-y-auto'
          >
            <FormField
              control={form.control}
              name='designation'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('form.designation')}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t('form.designation_placeholder')}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='date'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('form.date')}</FormLabel>
                  <FormControl>
                    <DatePicker
                      value={field.value ? new Date(field.value) : undefined}
                      onChange={(date) => {
                        field.onChange(date ? date.toISOString().split('T')[0] : '')
                      }}
                      placeholder={t('form.select_date')}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='grid grid-cols-2 gap-4'>
              <FormField
                control={form.control}
                name='amount'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('form.amount')}</FormLabel>
                    <FormControl>
                      <Input
                        type='number'
                        step='0.01'
                        min='0'
                        placeholder='0.00'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='currency'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('form.currency')}</FormLabel>
                    <SelectDropdown
                      defaultValue={field.value}
                      onValueChange={field.onChange}
                      placeholder={t('form.select_currency')}
                      items={currencies.map(currency => ({
                        label: `${currency.symbol} ${currency.code}`,
                        value: currency.code,
                      }))}
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {convertedAmount && watchedCurrency !== 'CHF' && (
              <div className='p-3 bg-green-50 border border-green-200 rounded-lg'>
                <p className='text-sm text-green-700'>
                  <span className='font-medium'>{t('form.converted_to_chf')}:</span>{' '}
                  {convertedAmount.toLocaleString('de-CH', {
                    style: 'currency',
                    currency: 'CHF',
                  })}
                  {exchangeRate && (
                    <span className='ml-2 text-green-600'>
                      ({t('form.rate')}: {exchangeRate.toFixed(4)})
                    </span>
                  )}
                </p>
              </div>
            )}

            <FormField
              control={form.control}
              name='category'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('form.category')}</FormLabel>
                  <SelectDropdown
                    defaultValue={field.value}
                    onValueChange={field.onChange}
                    placeholder={t('form.select_category')}
                    items={expenseCategories.map(category => ({
                      label: category.label,
                      value: category.value,
                    }))}
                  />
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* File Upload */}
            <div className='space-y-2'>
              <Label htmlFor='receipt-upload'>{t('form.receipt_optional')}</Label>
              <div className='space-y-2'>
                {!selectedFile ? (
                  <div className='border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center'>
                    <IconUpload className='h-8 w-8 mx-auto mb-2 text-muted-foreground' />
                    <div className='space-y-1'>
                      <p className='text-sm font-medium'>{t('form.upload_receipt')}</p>
                      <p className='text-xs text-muted-foreground'>
                        {t('form.file_types_size')}
                      </p>
                    </div>
                    <Input
                      id='receipt-upload'
                      type='file'
                      accept='.pdf,.jpg,.jpeg,.png'
                      onChange={handleFileChange}
                      className='mt-2'
                    />
                  </div>
                ) : (
                  <div className='flex items-center justify-between p-3 border rounded-lg bg-muted/50'>
                    <div className='flex items-center space-x-2'>
                      <IconUpload className='h-4 w-4 text-muted-foreground' />
                      <span className='text-sm font-medium'>{selectedFile.name}</span>
                      <span className='text-xs text-muted-foreground'>
                        ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
                      </span>
                    </div>
                    <Button
                      type='button'
                      variant='ghost'
                      size='sm'
                      onClick={removeFile}
                    >
                      <IconX className='h-4 w-4' />
                    </Button>
                  </div>
                )}
              </div>
            </div>

            <FormField
              control={form.control}
              name='comment'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('form.comment_optional')}</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={t('form.comment_placeholder')}
                      className='resize-none'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>

        <SheetFooter className='gap-2'>
          <SheetClose asChild>
            <Button variant='outline'>{t('buttons.cancel')}</Button>
          </SheetClose>
          <Button form='expense-form' type='submit' disabled={loading}>
            {isUpdate ? t('buttons.update') : t('buttons.add')} {t('form.expense')}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  )
}
