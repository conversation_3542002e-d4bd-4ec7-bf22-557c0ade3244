import { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { IconBuilding, IconLoader2 } from '@tabler/icons-react'

import { But<PERSON> } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'

import { Client, ClientForm, clientFormSchema, UpdateClientRequest } from '../data/schema'
import { useCreateClient, useUpdateClient } from '../hooks/use-clients'
import { useTranslations } from '@/context/i18n-context'

interface ClientMutateDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow?: Client
}

export function ClientMutateDrawer({
  open,
  onOpenChange,
  currentRow,
}: ClientMutateDialogProps) {
  const { t } = useTranslations('clients')
  const isEdit = !!currentRow
  const title = isEdit ? t('form.edit_client') : t('form.add_new_client')
  const description = isEdit
    ? t('form.update_client_description')
    : t('form.add_client_description')

  const createClientMutation = useCreateClient()
  const updateClientMutation = useUpdateClient()

  const form = useForm<ClientForm>({
    resolver: zodResolver(clientFormSchema),
    defaultValues: {
      name: '',
      code: '',
      description: '',
      contactEmail: '',
      contactPhone: '',
      address: '',
      isActive: true,
    },
  })

  const isLoading = createClientMutation.isPending || updateClientMutation.isPending

  // Reset form when opening/closing or changing currentRow
  useEffect(() => {
    if (open && currentRow) {
      form.reset({
        name: currentRow.name,
        code: currentRow.code,
        description: currentRow.description || '',
        contactEmail: currentRow.contactEmail || '',
        contactPhone: currentRow.contactPhone || '',
        address: currentRow.address || '',
        isActive: currentRow.isActive,
      })
    } else if (open && !currentRow) {
      form.reset({
        name: '',
        code: '',
        description: '',
        contactEmail: '',
        contactPhone: '',
        address: '',
        isActive: true,
      })
    }
  }, [open, currentRow, form])

  const onSubmit = (data: ClientForm) => {
    if (isEdit && currentRow) {
      // For updates, we don't include the id in the data payload
      const updateData: UpdateClientRequest = {
        name: data.name,
        code: data.code,
        description: data.description,
        contactEmail: data.contactEmail,
        contactPhone: data.contactPhone,
        address: data.address,
        isActive: data.isActive,
      }
      updateClientMutation.mutate(
        { id: currentRow.id, data: updateData },
        {
          onSuccess: () => {
            form.reset()
            onOpenChange(false)
          },
        }
      )
    } else {
      createClientMutation.mutate(data, {
        onSuccess: () => {
          form.reset()
          onOpenChange(false)
        },
      })
    }
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(state) => {
        form.reset()
        onOpenChange(state)
      }}
    >
      <DialogContent className='sm:max-w-2xl'>
        <DialogHeader className='text-left'>
          <div className='flex items-center gap-3'>
            <div className='flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10'>
              <IconBuilding className='h-5 w-5 text-primary' />
            </div>
            <div>
              <DialogTitle>{title}</DialogTitle>
              <DialogDescription>{description}</DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className='-mr-4 h-[26.25rem] w-full overflow-y-auto py-1 pr-4'>
          <Form {...form}>
            <form
              id='client-form'
              onSubmit={form.handleSubmit(onSubmit)}
              className='space-y-4 p-0.5'
            >
              <FormField
                control={form.control}
                name='name'
                render={({ field }) => (
                  <FormItem className='grid grid-cols-6 items-center gap-x-4 gap-y-1 space-y-0'>
                    <FormLabel className='col-span-2 text-right'>
                      {t('form.client_name')} *
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t('form.enter_client_name')}
                        className='col-span-4'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage className='col-span-4 col-start-3' />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='code'
                render={({ field }) => (
                  <FormItem className='grid grid-cols-6 items-center gap-x-4 gap-y-1 space-y-0'>
                    <FormLabel className='col-span-2 text-right'>
                      {t('form.client_code')} *
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t('form.client_code_placeholder')}
                        className='col-span-4'
                        {...field}
                        onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                      />
                    </FormControl>
                    <FormMessage className='col-span-4 col-start-3' />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='description'
                render={({ field }) => (
                  <FormItem className='grid grid-cols-6 items-center gap-x-4 gap-y-1 space-y-0'>
                    <FormLabel className='col-span-2 text-right'>
                      {t('form.description')}
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder={t('form.enter_client_description')}
                        className='col-span-4 min-h-[80px]'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage className='col-span-4 col-start-3' />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='contactEmail'
                render={({ field }) => (
                  <FormItem className='grid grid-cols-6 items-center gap-x-4 gap-y-1 space-y-0'>
                    <FormLabel className='col-span-2 text-right'>
                      {t('form.contact_email')}
                    </FormLabel>
                    <FormControl>
                      <Input
                        type='email'
                        placeholder={t('form.contact_email_placeholder')}
                        className='col-span-4'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage className='col-span-4 col-start-3' />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='contactPhone'
                render={({ field }) => (
                  <FormItem className='grid grid-cols-6 items-center gap-x-4 gap-y-1 space-y-0'>
                    <FormLabel className='col-span-2 text-right'>
                      {t('form.contact_phone')}
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t('form.contact_phone_placeholder')}
                        className='col-span-4'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage className='col-span-4 col-start-3' />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='address'
                render={({ field }) => (
                  <FormItem className='grid grid-cols-6 items-center gap-x-4 gap-y-1 space-y-0'>
                    <FormLabel className='col-span-2 text-right'>
                      {t('form.address')}
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder={t('form.enter_client_address')}
                        className='col-span-4 min-h-[60px]'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage className='col-span-4 col-start-3' />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='isActive'
                render={({ field }) => (
                  <FormItem className='grid grid-cols-6 items-center gap-x-4 gap-y-1 space-y-0'>
                    <FormLabel className='col-span-2 text-right'>
                      {t('form.active_status')}
                    </FormLabel>
                    <div className='col-span-4 flex items-center justify-between rounded-lg border p-3'>
                      <div className='space-y-0.5'>
                        <div className='text-sm text-muted-foreground'>
                          {t('form.enable_client_description')}
                        </div>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </div>
                    <FormMessage className='col-span-4 col-start-3' />
                  </FormItem>
                )}
              />
            </form>
          </Form>
        </div>
        <DialogFooter>
          <Button
            type='button'
            variant='outline'
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            {t('buttons.cancel')}
          </Button>
          <Button type='submit' form='client-form' disabled={isLoading}>
            {isLoading && <IconLoader2 className='mr-2 h-4 w-4 animate-spin' />}
            {isLoading
              ? (isEdit ? t('actions.updating') : t('actions.creating'))
              : (isEdit ? t('form.update_client') : t('form.create_client'))
            }
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
