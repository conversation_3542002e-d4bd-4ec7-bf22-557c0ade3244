import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { LanguageSwitcher } from '@/components/language-switcher'
import { useTranslations, useErrorTranslations } from '@/context/i18n-context'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useNonWorkingDaysColumns } from './components/non-working-days-columns'
import { NonWorkingDaysDataTable } from './components/non-working-days-data-table'
import { NonWorkingDaysDialogs } from './components/non-working-days-dialogs'
import { NonWorkingDaysPrimaryButtons } from './components/non-working-days-primary-buttons'
import NonWorkingDaysProvider from './context/non-working-days-context'
import { useMyLeaveRequests } from './hooks/use-leaves'

export default function NonWorkingDays() {
  const { data: leaveRequests, isLoading, error } = useMyLeaveRequests()
  const { t } = useTranslations('non-working-days')
  const { t: errorT } = useErrorTranslations()
  const nonWorkingDaysColumns = useNonWorkingDaysColumns()

  return (
    <NonWorkingDaysProvider>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <LanguageSwitcher />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-2 flex flex-wrap items-center justify-between gap-x-4 space-y-2'>
          <div>
            <h2 className='text-2xl font-bold tracking-tight'>{t('title')}</h2>
            <p className='text-muted-foreground'>
              {t('subtitle')}
            </p>
          </div>
          <NonWorkingDaysPrimaryButtons />
        </div>

        <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0'>
          {isLoading ? (
            <div className='flex items-center justify-center py-8'>
              <LoadingSpinner className='h-8 w-8' />
              <span className='ml-2'>{t('messages.loading_requests')}</span>
            </div>
          ) : error ? (
            <Alert variant='destructive'>
              <AlertDescription>
                {errorT('general.something_went_wrong')}
              </AlertDescription>
            </Alert>
          ) : (
            <NonWorkingDaysDataTable
              data={leaveRequests || []}
              columns={nonWorkingDaysColumns}
            />
          )}
        </div>
      </Main>

      <NonWorkingDaysDialogs />
    </NonWorkingDaysProvider>
  )
}
