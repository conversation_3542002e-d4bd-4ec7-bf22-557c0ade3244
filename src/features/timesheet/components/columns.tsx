import { ColumnDef } from '@tanstack/react-table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { IconDots, IconEdit, IconTrash } from '@tabler/icons-react'
import { TimeEntry } from '../data/schema'
import { useCommonTranslations } from '@/context/i18n-context'
import { useTimesheet } from '../context/timesheet-context'

export function useTimesheetColumns(): ColumnDef<TimeEntry>[] {
  const { t } = useCommonTranslations()

  return [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label={t('table.actions.select_all')}
        className='translate-y-[2px]'
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label={t('table.actions.select_row')}
        className='translate-y-[2px]'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'date',
    header: t('table.columns.date'),
    cell: ({ row }) => {
      const date = new Date(row.getValue('date'))
      return (
        <div className='font-medium'>
          {date.toLocaleDateString('en-US', {
            weekday: 'short',
            month: 'short',
            day: 'numeric',
          })}
        </div>
      )
    },
  },
  {
    accessorKey: 'hours',
    header: t('table.columns.hours'),
    cell: ({ row }) => {
      const hours = parseFloat(row.getValue('hours'))
      return (
        <div className='font-medium'>
          {hours}h
        </div>
      )
    },
  },
  {
    accessorKey: 'location',
    header: t('table.columns.location'),
    cell: ({ row }) => {
      const location = row.getValue('location') as string
      return (
        <Badge variant={location === 'home' ? 'secondary' : 'default'}>
          {location === 'home' ? `🏠 ${t('locations.home')}` : `🏢 ${t('locations.office')}`}
        </Badge>
      )
    },
  },
  {
    accessorKey: 'client',
    header: t('table.columns.client'),
    cell: ({ row }) => (
      <div className='font-medium'>{row.getValue('client')}</div>
    ),
  },
  {
    accessorKey: 'project',
    header: t('table.columns.project'),
    cell: ({ row }) => {
      const project = row.getValue('project') as string
      return project ? (
        <div className='text-muted-foreground'>{project}</div>
      ) : (
        <div className='text-muted-foreground italic'>{t('table.empty_states.no_project')}</div>
      )
    },
  },
  {
    accessorKey: 'description',
    header: t('table.columns.description'),
    cell: ({ row }) => {
      const description = row.getValue('description') as string
      return description ? (
        <div className='max-w-[200px] truncate text-muted-foreground'>
          {description}
        </div>
      ) : (
        <div className='text-muted-foreground italic'>{t('table.empty_states.no_description')}</div>
      )
    },
  },
  {
    id: 'actions',
    enableHiding: false,
    cell: ({ row }) => {
      const timeEntry = row.original
      // eslint-disable-next-line react-hooks/rules-of-hooks
      const { setOpen, setCurrentRow } = useTimesheet()

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='ghost' className='h-8 w-8 p-0'>
              <span className='sr-only'>{t('aria_labels.open_menu')}</span>
              <IconDots className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuLabel>{t('table.columns.actions')}</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => {
                setCurrentRow(timeEntry)
                setOpen('update')
              }}
            >
              <IconEdit className='mr-2 h-4 w-4' />
              {t('table.actions.edit')}
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => {
                setCurrentRow(timeEntry)
                setOpen('delete')
              }}
              className='text-destructive'
            >
              <IconTrash className='mr-2 h-4 w-4' />
              {t('table.actions.delete')}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
  ]
}
