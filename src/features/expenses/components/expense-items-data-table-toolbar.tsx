import { Table } from '@tanstack/react-table'
import { IconX } from '@tabler/icons-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { DataTableViewOptions } from '@/features/timesheet/components/data-table-view-options'
import { DataTableFacetedFilter } from '@/features/timesheet/components/data-table-faceted-filter'
import { useCommonTranslations } from '@/context/i18n-context'

interface DataTableToolbarProps<TData> {
  table: Table<TData>
}

export function ExpenseItemsDataTableToolbar<TData>({
  table,
}: DataTableToolbarProps<TData>) {
  const { t } = useCommonTranslations()
  const isFiltered = table.getState().columnFilters.length > 0

  const statusOptions = [
    {
      label: `📝 ${t('expense_status.draft')}`,
      value: 'draft',
    },
    {
      label: `📤 ${t('expense_status.submitted')}`,
      value: 'submitted',
    },
    {
      label: `✅ ${t('expense_status.approved')}`,
      value: 'approved',
    },
    {
      label: `❌ ${t('expense_status.rejected')}`,
      value: 'rejected',
    },
  ]

  const clientOptions = [
    {
      label: 'Acme Corporation',
      value: 'Acme Corporation',
    },
    {
      label: 'TechStart Inc.',
      value: 'TechStart Inc.',
    },
    {
      label: 'Global Solutions Ltd.',
      value: 'Global Solutions Ltd.',
    },
    {
      label: 'Innovation Labs',
      value: 'Innovation Labs',
    },
    {
      label: 'Digital Dynamics',
      value: 'Digital Dynamics',
    },
  ]

  return (
    <div className='flex items-center justify-between'>
      <div className='flex flex-1 items-center space-x-2'>
        <Input
          placeholder={t('table.filters.filter_by_title')}
          value={(table.getColumn('title')?.getFilterValue() as string) ?? ''}
          onChange={(event) =>
            table.getColumn('title')?.setFilterValue(event.target.value)
          }
          className='h-8 w-[150px] lg:w-[250px]'
        />
        {table.getColumn('status') && (
          <DataTableFacetedFilter
            column={table.getColumn('status')}
            title={t('table.filters.status')}
            options={statusOptions}
          />
        )}
        {table.getColumn('client') && (
          <DataTableFacetedFilter
            column={table.getColumn('client')}
            title={t('table.filters.client')}
            options={clientOptions}
          />
        )}
        {isFiltered && (
          <Button
            variant='ghost'
            onClick={() => table.resetColumnFilters()}
            className='h-8 px-2 lg:px-3'
          >
            {t('buttons.refresh')}
            <IconX className='ml-2 h-4 w-4' />
          </Button>
        )}
      </div>
      <DataTableViewOptions table={table} />
    </div>
  )
}
