import {
  Sidebar,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  SidebarHeader,
  SidebarRail,
} from '@/components/ui/sidebar'
import { NavGroup } from '@/components/layout/nav-group'
import { NavUser } from '@/components/layout/nav-user'
import { TeamSwitcher } from '@/components/layout/team-switcher'
import { useUser } from '@/context/user-context'
import { useCommonTranslations } from '@/context/i18n-context'
import { generateSidebarData } from './data/dynamic-sidebar-data'
import { sidebarData as fallbackSidebarData } from './data/sidebar-data'

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { user, permissions, isAuthenticated } = useUser()
  const { t } = useCommonTranslations()

  // Generate dynamic sidebar data based on user permissions
  const sidebarData = isAuthenticated
    ? generateSidebarData(user, permissions, isAuthenticated, t)
    : fallbackSidebarData

  return (
    <Sidebar collapsible='icon' variant='floating' {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={sidebarData.teams} />
      </SidebarHeader>
      <SidebarContent>
        {sidebarData.navGroups.map((props) => (
          <NavGroup key={props.title} {...props} />
        ))}
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={sidebarData.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
