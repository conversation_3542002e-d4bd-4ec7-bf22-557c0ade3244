import { But<PERSON> } from '@/components/ui/button'
import { IconPlus } from '@tabler/icons-react'
import { useNonWorkingDays } from '../context/non-working-days-context'
import { useCommonTranslations } from '@/context/i18n-context'

export function NonWorkingDaysPrimaryButtons() {
  const { setOpen } = useNonWorkingDays()
  const { t } = useCommonTranslations()

  return (
    <div className='flex items-center space-x-2'>
      <Button onClick={() => setOpen('create')}>
        <IconPlus className='mr-2 h-4 w-4' />
        {t('buttons.request_leave')}
      </Button>
    </div>
  )
}
