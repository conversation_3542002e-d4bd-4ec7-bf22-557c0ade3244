import { ColumnDef } from '@tanstack/react-table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { DataTableColumnHeader } from '@/features/tasks/components/data-table-column-header'
import { IconDots, IconEdit, IconTrash, IconCheck, IconX } from '@tabler/icons-react'
import { LeaveRequest, leaveTypeOptions, statusOptions } from '../data/schema'
import { useNonWorkingDays } from '../context/non-working-days-context'
import { useApproveLeaveRequest, useRejectLeaveRequest, useDeleteLeaveRequest } from '../hooks/use-leaves'
import { format } from 'date-fns'
import { useCommonTranslations } from '@/context/i18n-context'
import { useDateLocale } from '@/lib/date-locale'

const leaveTypeColors = {
  annual: 'default',
  sick: 'destructive',
  maternity: 'secondary',
  paternity: 'secondary',
  unpaid: 'outline',
  other: 'outline',
} as const

const statusColors = {
  pending: 'secondary',
  approved: 'default',
  rejected: 'destructive',
} as const

function ActionsCell({ row }: { row: { original: LeaveRequest } }) {
  const { t } = useCommonTranslations()
  const { setOpen, setCurrentItem } = useNonWorkingDays()
  const approveLeaveRequest = useApproveLeaveRequest()
  const rejectLeaveRequest = useRejectLeaveRequest()
  const deleteLeaveRequest = useDeleteLeaveRequest()
  const item = row.original

  // Get user role from localStorage
  const roles = localStorage.getItem('roles')
  const isAdmin = roles?.includes('admin') || false

  const handleEdit = () => {
    setCurrentItem(item)
    setOpen('update')
  }

  const handleDelete = () => {
    deleteLeaveRequest.mutate(item.id)
  }

  const handleApprove = () => {
    approveLeaveRequest.mutate(item.id)
  }

  const handleReject = () => {
    rejectLeaveRequest.mutate({ id: item.id })
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant='ghost' className='h-8 w-8 p-0'>
          <span className='sr-only'>Open menu</span>
          <IconDots className='h-4 w-4' />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align='end'>
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuSeparator />

        {/* Admin actions for pending requests */}
        {isAdmin && item.status === 'pending' && (
          <>
            <DropdownMenuItem onClick={handleApprove}>
              <IconCheck className='mr-2 h-4 w-4' />
              Approve
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleReject}>
              <IconX className='mr-2 h-4 w-4' />
              Reject
            </DropdownMenuItem>
            <DropdownMenuSeparator />
          </>
        )}

        {/* Edit action - only for pending requests */}
        {item.status === 'pending' && (
          <DropdownMenuItem onClick={handleEdit}>
            <IconEdit className='mr-2 h-4 w-4' />
            {t('table.actions.edit')}
          </DropdownMenuItem>
        )}

        <DropdownMenuItem onClick={handleDelete} className='text-destructive'>
          <IconTrash className='mr-2 h-4 w-4' />
          {t('table.actions.delete')}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export function useNonWorkingDaysColumns(): ColumnDef<LeaveRequest>[] {
  const { t } = useCommonTranslations()
  const locale = useDateLocale()

  return [
  {
    accessorKey: 'startDate',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title={t('table.columns.start_date')} />
    ),
    cell: ({ row }) => {
      const date = new Date(row.getValue('startDate'))
      return (
        <div className='font-medium'>
          {format(date, 'MMM dd, yyyy', { locale })}
        </div>
      )
    },
  },
  {
    accessorKey: 'endDate',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title={t('table.columns.end_date')} />
    ),
    cell: ({ row }) => {
      const startDate = row.getValue('startDate') as string
      const endDate = row.getValue('endDate') as string

      if (startDate === endDate) {
        return <div className='text-muted-foreground'>{t('time.same_day')}</div>
      }

      const date = new Date(endDate)
      return (
        <div className='font-medium'>
          {format(date, 'MMM dd, yyyy', { locale })}
        </div>
      )
    },
  },
  {
    accessorKey: 'leaveType',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title={t('common.type')} />
    ),
    cell: ({ row }) => {
      const leaveType = row.getValue('leaveType') as string
      const leaveTypeOption = leaveTypeOptions.find(option => option.value === leaveType)
      const color = leaveTypeColors[leaveType as keyof typeof leaveTypeColors] || 'default'

      return (
        <Badge variant={color}>
          {leaveTypeOption?.label || leaveType}
        </Badge>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: 'status',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title={t('table.columns.status')} />
    ),
    cell: ({ row }) => {
      const status = row.getValue('status') as string
      const statusOption = statusOptions.find(option => option.value === status)
      const color = statusColors[status as keyof typeof statusColors] || 'default'

      return (
        <Badge variant={color}>
          {statusOption?.label || status}
        </Badge>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: 'reason',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title={t('common.description')} />
    ),
    cell: ({ row }) => {
      const reason = row.getValue('reason') as string
      return reason ? (
        <div className='max-w-[200px] truncate' title={reason}>
          {reason}
        </div>
      ) : (
        <div className='text-muted-foreground'>-</div>
      )
    },
  },
  {
    accessorKey: 'duration',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title={t('common.time')} />
    ),
    cell: ({ row }) => {
      const leaveRequest = row.original

      // Use numberOfDays from API if available, otherwise calculate
      let days: number
      if (leaveRequest.numberOfDays) {
        days = leaveRequest.numberOfDays
      } else {
        const startDate = new Date(leaveRequest.startDate)
        const endDate = new Date(leaveRequest.endDate)
        const diffTime = Math.abs(endDate.getTime() - startDate.getTime())
        days = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1
      }

      return (
        <div className='font-medium'>
          {days} {days === 1 ? t('time.day') : t('time.days')}
        </div>
      )
    },
  },
  {
    id: 'actions',
    enableHiding: false,
    cell: ActionsCell,
  },
  ]
}
