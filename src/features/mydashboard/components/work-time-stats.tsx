import { Clock, MapPin, Calendar, TrendingUp } from 'lucide-react'
import { mockTimeEntries } from '@/features/timesheet/data/mock-data'
import { useTranslations } from '@/context/i18n-context'

export function WorkTimeStats() {
  const { t } = useTranslations('mydashboard')

  // Calculate statistics from mock data
  const totalHours = mockTimeEntries.reduce((sum, entry) => sum + entry.hours, 0)
  const totalDays = mockTimeEntries.length
  const averageHoursPerDay = totalDays > 0 ? (totalHours / totalDays).toFixed(1) : 0

  const remoteHours = mockTimeEntries
    .filter(entry => entry.location === 'home')
    .reduce((sum, entry) => sum + entry.hours, 0)

  const officeHours = mockTimeEntries
    .filter(entry => entry.location === 'office')
    .reduce((sum, entry) => sum + entry.hours, 0)

  // Get unique clients
  const uniqueClients = new Set(mockTimeEntries.map(entry => entry.client)).size

  const stats = [
    {
      label: t('work_time.total_hours'),
      value: `${totalHours}h`,
      icon: Clock,
      description: t('work_time.this_month'),
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      label: t('work_time.working_days'),
      value: totalDays.toString(),
      icon: Calendar,
      description: t('work_time.days_logged'),
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      label: t('work_time.avg_hours_day'),
      value: `${averageHoursPerDay}h`,
      icon: TrendingUp,
      description: t('work_time.daily_average'),
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      label: t('work_time.active_clients'),
      value: uniqueClients.toString(),
      icon: MapPin,
      description: t('work_time.different_clients'),
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
  ]

  return (
    <div className='space-y-4'>
      <div className='grid grid-cols-2 gap-4 lg:grid-cols-4'>
        {stats.map((stat) => {
          const Icon = stat.icon
          return (
            <div key={stat.label} className='space-y-2'>
              <div className='flex items-center gap-2'>
                <div className={`rounded-lg p-2 ${stat.bgColor}`}>
                  <Icon className={`h-4 w-4 ${stat.color}`} />
                </div>
                <div className='flex-1 min-w-0'>
                  <p className='text-sm font-medium text-muted-foreground truncate'>
                    {stat.label}
                  </p>
                  <p className='text-lg font-bold'>{stat.value}</p>
                </div>
              </div>
              <p className='text-xs text-muted-foreground'>{stat.description}</p>
            </div>
          )
        })}
      </div>

      {/* Additional breakdown */}
      <div className='rounded-lg border p-4 space-y-3'>
        <h4 className='font-medium'>Location Breakdown</h4>
        <div className='space-y-2'>
          <div className='flex justify-between items-center'>
            <span className='text-sm text-muted-foreground'>Remote Work</span>
            <span className='font-medium'>{remoteHours}h</span>
          </div>
          <div className='flex justify-between items-center'>
            <span className='text-sm text-muted-foreground'>Office Work</span>
            <span className='font-medium'>{officeHours}h</span>
          </div>
        </div>
      </div>

      {/* Recent activity */}
      <div className='rounded-lg border p-4 space-y-3'>
        <h4 className='font-medium'>Recent Activity</h4>
        <div className='space-y-2'>
          {mockTimeEntries.slice(0, 3).map((entry) => (
            <div key={entry.id} className='flex justify-between items-center text-sm'>
              <div className='flex items-center gap-2'>
                <div className={`h-2 w-2 rounded-full ${
                  entry.location === 'home' ? 'bg-blue-500' : 'bg-green-500'
                }`} />
                <span className='text-muted-foreground'>
                  {new Date(entry.date).toLocaleDateString()}
                </span>
                <span>{entry.client}</span>
              </div>
              <span className='font-medium'>{entry.hours}h</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
