import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Textarea } from '@/components/ui/textarea'
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet'
import { SelectDropdown } from '@/components/select-dropdown'
import { DatePickerWithRange } from '@/components/ui/date-picker-with-range'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { leaveRequestFormSchema, LeaveRequestForm, LeaveRequest, leaveTypeOptions, calculateNumberOfDays } from '../data/schema'
import { useCreateLeaveRequest, useUpdateLeaveRequest } from '../hooks/use-leaves'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentItem?: LeaveRequest
}

export function NonWorkingDayMutateDrawer({ open, onOpenChange, currentItem }: Props) {
  const isUpdate = !!currentItem

  const createLeaveRequest = useCreateLeaveRequest()
  const updateLeaveRequest = useUpdateLeaveRequest()

  const form = useForm<LeaveRequestForm>({
    resolver: zodResolver(leaveRequestFormSchema),
    defaultValues: currentItem ? {
      dateRange: {
        from: new Date(currentItem.startDate),
        to: new Date(currentItem.endDate),
      },
      leaveType: currentItem.leaveType,
      reason: currentItem.reason || '',
      comment: '', // Comment is not stored in the current schema, so default to empty
    } : {
      dateRange: {
        from: undefined,
        to: undefined,
      },
      leaveType: 'annual',
      reason: '',
      comment: '',
    },
  })

  const watchedLeaveType = form.watch('leaveType')
  const watchedDateRange = form.watch('dateRange')

  // Calculate number of days for display
  const numberOfDays = watchedDateRange?.from && watchedDateRange?.to
    ? calculateNumberOfDays(watchedDateRange.from, watchedDateRange.to)
    : 0

  const onSubmit = async (data: LeaveRequestForm) => {
    try {
      // Get consultant ID from localStorage
      const consultantInfo = localStorage.getItem('consultantInfo')
      if (!consultantInfo) {
        throw new Error('User information not found')
      }
      const { id: consultantId } = JSON.parse(consultantInfo)

      // Validate dates are selected
      if (!data.dateRange.from || !data.dateRange.to) {
        throw new Error('Please select both start and end dates')
      }

      // Calculate number of days
      const numberOfDays = calculateNumberOfDays(data.dateRange.from, data.dateRange.to)

      // Transform form data to API format
      const apiData = {
        consultantId,
        startDate: data.dateRange.from.toISOString().split('T')[0],
        endDate: data.dateRange.to.toISOString().split('T')[0],
        numberOfDays,
        leaveType: data.leaveType,
        reason: data.reason || undefined,
        // Remove comment for now since API might not support it
        // comment: data.comment || undefined,
      }

      if (isUpdate && currentItem) {
        await updateLeaveRequest.mutateAsync({
          id: currentItem.id,
          ...apiData,
        })
      } else {
        await createLeaveRequest.mutateAsync(apiData)
      }

      // Close the drawer
      onOpenChange(false)

      // Reset form after a short delay to allow close animation
      setTimeout(() => {
        form.reset()
      }, 300)
    } catch (error) {
      // Error handling is done in the hooks
      console.error('Error submitting leave request:', error)
    }
  }

  const isLoading = createLeaveRequest.isPending || updateLeaveRequest.isPending

  return (
    <Sheet
      open={open}
      onOpenChange={(v) => {
        onOpenChange(v)
        if (!v) form.reset()
      }}
    >
      <SheetContent className='flex flex-col'>
        <SheetHeader className='text-left'>
          <SheetTitle>{isUpdate ? 'Update' : 'Create'} Leave Request</SheetTitle>
          <SheetDescription>
            {isUpdate
              ? 'Update your leave request details below.'
              : 'Submit a new leave request for approval.'}
            Click save when you&apos;re done.
          </SheetDescription>
        </SheetHeader>

        <Form {...form}>
          <form
            id='non-working-day-form'
            onSubmit={form.handleSubmit(onSubmit)}
            className='flex-1 space-y-6'
          >
            <FormField
              control={form.control}
              name='dateRange'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('date_picker.leave_period')}</FormLabel>
                  <FormControl>
                    <DatePickerWithRange
                      value={field.value}
                      onChange={field.onChange}
                      placeholder={t('date_picker.select_leave_period')}
                    />
                  </FormControl>
                  {numberOfDays > 0 && (
                    <div className="text-sm text-muted-foreground mt-2">
                      <span className="font-medium">{t('date_picker.duration')}:</span> {numberOfDays} {numberOfDays === 1 ? t('date_picker.day') : t('date_picker.days')}
                    </div>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='leaveType'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Leave Type</FormLabel>
                  <FormControl>
                    <SelectDropdown
                      defaultValue={field.value}
                      onValueChange={(value) => {
                        field.onChange(value)
                        // Clear reason when changing away from 'other'
                        if (value !== 'other') {
                          form.setValue('reason', '')
                        }
                      }}
                      placeholder='Select leave type'
                      items={leaveTypeOptions.map(option => ({
                        label: option.label,
                        value: option.value,
                      }))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {(watchedLeaveType === 'other' || watchedLeaveType === 'sick') && (
              <FormField
                control={form.control}
                name='reason'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {watchedLeaveType === 'other' ? 'Reason (Required)' : 'Additional Details (Optional)'}
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder={
                          watchedLeaveType === 'other'
                            ? "Please specify the reason..."
                            : "Additional details about your leave..."
                        }
                        className='resize-none'
                        rows={3}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name='comment'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Comment (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Add any additional comments about your leave request..."
                      className='resize-none'
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>

        <SheetFooter className='gap-2'>
          <SheetClose asChild>
            <Button variant='outline' disabled={isLoading}>Cancel</Button>
          </SheetClose>
          <Button form='non-working-day-form' type='submit' disabled={isLoading}>
            {isLoading ? (
              <>
                <LoadingSpinner className="mr-2 h-4 w-4" />
                {isUpdate ? 'Updating...' : 'Creating...'}
              </>
            ) : (
              `${isUpdate ? 'Update' : 'Create'} Leave Request`
            )}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  )
}
