"use client"

import * as React from "react"
import { format } from "date-fns"
import { CalendarIcon } from "lucide-react"
import { DateRange } from "react-day-picker"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { useDateLocale } from "@/lib/date-locale"
import { useCommonTranslations } from "@/context/i18n-context"

interface DatePickerWithRangeProps {
  className?: string
  value?: DateRange
  onChange?: (date: DateRange | undefined) => void
  placeholder?: string
  disabled?: boolean
}

export function DatePickerWithRange({
  className,
  value,
  onChange,
  placeholder,
  disabled = false,
}: DatePickerWithRangeProps) {
  const [date, setDate] = React.useState<DateRange | undefined>(value)
  const locale = useDateLocale()
  const { t } = useCommonTranslations()

  const defaultPlaceholder = placeholder || t('date_picker.pick_date_range')

  React.useEffect(() => {
    setDate(value)
  }, [value])

  const handleDateSelect = (newDate: DateRange | undefined) => {
    setDate(newDate)
    onChange?.(newDate)
  }

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant="outline"
            disabled={disabled}
            className={cn(
              "w-full justify-start text-left font-normal",
              !date && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date?.from ? (
              date.to && date.to.getTime() !== date.from.getTime() ? (
                <>
                  {format(date.from, "PPP", { locale })} -{" "}
                  {format(date.to, "PPP", { locale })}
                </>
              ) : (
                format(date.from, "PPP", { locale })
              )
            ) : (
              <span>{defaultPlaceholder}</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={date?.from}
            selected={date}
            onSelect={handleDateSelect}
            numberOfMonths={1}
            locale={locale}
          />
        </PopoverContent>
      </Popover>
    </div>
  )
}
