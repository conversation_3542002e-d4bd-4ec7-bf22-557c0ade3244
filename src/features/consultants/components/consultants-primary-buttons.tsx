import { IconUserPlus } from '@tabler/icons-react'
import { But<PERSON> } from '@/components/ui/button'
import { useConsultants } from '../context/consultants-context'
import { useUser } from '@/context/user-context'
import { useCommonTranslations } from '@/context/i18n-context'

export function ConsultantsPrimaryButtons() {
  const { setOpen } = useConsultants()
  const { permissions } = useUser()
  const { t } = useCommonTranslations()

  if (!permissions.canManageUsers) {
    return null
  }

  return (
    <div className='flex gap-2'>
      <Button className='space-x-1' onClick={() => setOpen('add')}>
        <span>{t('buttons.add_consultant')}</span> <IconUserPlus size={18} />
      </Button>
    </div>
  )
}
