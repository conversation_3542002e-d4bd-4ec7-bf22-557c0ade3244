import { IconDownload, IconPlus } from '@tabler/icons-react'
import { But<PERSON> } from '@/components/ui/button'
import { useExpenses } from '../context/expenses-context'
import { useCommonTranslations } from '@/context/i18n-context'

export function ExpensesPrimaryButtons() {
  const { setOpen } = useExpenses()
  const { t } = useCommonTranslations()

  return (
    <div className='flex gap-2'>
      <Button
        variant='outline'
        className='space-x-1'
        onClick={() => {
          // TODO: Implement export functionality
          console.log('Export expenses')
        }}
      >
        <span>{t('buttons.export')}</span> <IconDownload size={18} />
      </Button>
      <Button className='space-x-1' onClick={() => setOpen('create-item')}>
        <span>{t('buttons.new_expense_item')}</span> <IconPlus size={18} />
      </Button>
    </div>
  )
}
