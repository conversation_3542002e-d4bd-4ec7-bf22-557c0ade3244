import { enUS, fr } from 'date-fns/locale'
import { useI18n } from '@/context/i18n-context'

// Map of language codes to date-fns locales
const localeMap = {
  en: enUS,
  fr: fr,
}

// Hook to get the current date-fns locale
export function useDateLocale() {
  const { language } = useI18n()
  return localeMap[language as keyof typeof localeMap] || enUS
}

// Function to get locale without hook (for use in non-component contexts)
export function getDateLocale(language: string) {
  return localeMap[language as keyof typeof localeMap] || enUS
}
