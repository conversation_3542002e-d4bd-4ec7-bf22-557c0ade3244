import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from '@/hooks/use-toast'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  Sheet<PERSON>ooter,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet'
import { SelectDropdown } from '@/components/select-dropdown'
import { DatePicker } from '@/components/ui/date-picker'
import { expenseItemFormSchema, ExpenseItemForm, ExpenseItem } from '../data/schema'
import { mockClients } from '../data/mock-data'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentItem?: ExpenseItem
}

export function ExpenseItemMutateDrawer({ open, onOpenChange, currentItem }: Props) {
  const isUpdate = !!currentItem

  const form = useForm<ExpenseItemForm>({
    resolver: zodResolver(expenseItemFormSchema),
    defaultValues: currentItem ? {
      title: currentItem.title,
      startDate: currentItem.startDate,
      endDate: currentItem.endDate,
      client: currentItem.client,
      description: currentItem.description || '',
    } : {
      title: '',
      startDate: '',
      endDate: '',
      client: '',
      description: '',
    },
  })

  const onSubmit = (data: ExpenseItemForm) => {
    // TODO: Implement actual API call
    console.log('Submitting expense item:', data)

    onOpenChange(false)
    form.reset()

    toast({
      title: isUpdate ? 'Expense item updated' : 'Expense item created',
      description: `Successfully ${isUpdate ? 'updated' : 'created'} expense item "${data.title}".`,
    })
  }

  return (
    <Sheet
      open={open}
      onOpenChange={(v) => {
        onOpenChange(v)
        if (!v) form.reset()
      }}
    >
      <SheetContent className='flex flex-col'>
        <SheetHeader className='text-left'>
          <SheetTitle>{isUpdate ? 'Update' : 'Create'} Expense Item</SheetTitle>
          <SheetDescription>
            {isUpdate
              ? 'Update the expense item details below.'
              : 'Create a new expense item to group related expenses.'}
            Click save when you&apos;re done.
          </SheetDescription>
        </SheetHeader>

        <Form {...form}>
          <form
            id='expense-item-form'
            onSubmit={form.handleSubmit(onSubmit)}
            className='flex-1 space-y-6'
          >
            <FormField
              control={form.control}
              name='title'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='e.g., Client Meeting - Zurich'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='client'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Client</FormLabel>
                  <SelectDropdown
                    defaultValue={field.value}
                    onValueChange={field.onChange}
                    placeholder='Select a client'
                    items={mockClients.map(client => ({
                      label: client.name,
                      value: client.name,
                    }))}
                  />
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='grid grid-cols-2 gap-4'>
              <FormField
                control={form.control}
                name='startDate'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('date_picker.start_date')}</FormLabel>
                    <FormControl>
                      <DatePicker
                        value={field.value ? new Date(field.value) : undefined}
                        onChange={(date) => {
                          field.onChange(date ? date.toISOString().split('T')[0] : '')
                        }}
                        placeholder={t('date_picker.select_date')}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='endDate'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('date_picker.end_date')}</FormLabel>
                    <FormControl>
                      <DatePicker
                        value={field.value ? new Date(field.value) : undefined}
                        onChange={(date) => {
                          field.onChange(date ? date.toISOString().split('T')[0] : '')
                        }}
                        placeholder={t('date_picker.select_date')}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name='description'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder='Brief description of the expense item...'
                      className='resize-none'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>

        <SheetFooter className='gap-2'>
          <SheetClose asChild>
            <Button variant='outline'>Cancel</Button>
          </SheetClose>
          <Button form='expense-item-form' type='submit'>
            {isUpdate ? 'Update' : 'Create'} Item
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  )
}
