import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { LanguageSwitcher } from '@/components/language-switcher'
import { useTranslations } from '@/context/i18n-context'
import { useExpenseItemsColumns } from './components/expense-items-columns'
import { ExpenseItemsDataTable } from './components/expense-items-data-table'
import { ExpensesDialogs } from './components/expenses-dialogs'
import { ExpensesPrimaryButtons } from './components/expenses-primary-buttons'
import ExpensesProvider from './context/expenses-context'
import { mockExpenseItems } from './data/mock-data'

export default function Expenses() {
  const { t } = useTranslations('expenses')
  const expenseItemsColumns = useExpenseItemsColumns()

  return (
    <ExpensesProvider>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <LanguageSwitcher />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-2 flex flex-wrap items-center justify-between gap-x-4 space-y-2'>
          <div>
            <h2 className='text-2xl font-bold tracking-tight'>{t('title')}</h2>
            <p className='text-muted-foreground'>
              {t('subtitle')}
            </p>
          </div>
          <ExpensesPrimaryButtons />
        </div>
        <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0'>
          <ExpenseItemsDataTable data={mockExpenseItems} columns={expenseItemsColumns} />
        </div>
      </Main>

      <ExpensesDialogs />
    </ExpensesProvider>
  )
}
