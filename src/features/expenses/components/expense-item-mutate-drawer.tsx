import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from '@/hooks/use-toast'
import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  Sheet<PERSON>ooter,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet'
import { SelectDropdown } from '@/components/select-dropdown'
import { DatePicker } from '@/components/ui/date-picker'
import { expenseItemFormSchema, ExpenseItemForm, ExpenseItem } from '../data/schema'
import { mockClients } from '../data/mock-data'
import { useCommonTranslations } from '@/context/i18n-context'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentItem?: ExpenseItem
}

export function ExpenseItemMutateDrawer({ open, onOpenChange, currentItem }: Props) {
  const isUpdate = !!currentItem
  const { t } = useCommonTranslations()

  const form = useForm<ExpenseItemForm>({
    resolver: zodResolver(expenseItemFormSchema),
    defaultValues: currentItem ? {
      title: currentItem.title,
      startDate: currentItem.startDate,
      endDate: currentItem.endDate,
      client: currentItem.client,
      description: currentItem.description || '',
    } : {
      title: '',
      startDate: '',
      endDate: '',
      client: '',
      description: '',
    },
  })

  const onSubmit = (data: ExpenseItemForm) => {
    // TODO: Implement actual API call
    console.log('Submitting expense item:', data)

    onOpenChange(false)
    form.reset()

    toast({
      title: isUpdate ? 'Expense item updated' : 'Expense item created',
      description: `Successfully ${isUpdate ? 'updated' : 'created'} expense item "${data.title}".`,
    })
  }

  return (
    <Sheet
      open={open}
      onOpenChange={(v) => {
        onOpenChange(v)
        if (!v) form.reset()
      }}
    >
      <SheetContent className='flex flex-col'>
        <SheetHeader className='text-left'>
          <SheetTitle>{isUpdate ? t('expenses.update_expense_item') : t('expenses.create_expense_item')}</SheetTitle>
          <SheetDescription>
            {isUpdate
              ? t('expenses.update_expense_description')
              : t('expenses.create_expense_description')}
            {' '}{t('expenses.click_save_when_done')}
          </SheetDescription>
        </SheetHeader>

        <Form {...form}>
          <form
            id='expense-item-form'
            onSubmit={form.handleSubmit(onSubmit)}
            className='flex-1 space-y-6'
          >
            <FormField
              control={form.control}
              name='title'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('expenses.title')}</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t('expenses.title_placeholder')}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='client'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('common.client')}</FormLabel>
                  <SelectDropdown
                    defaultValue={field.value}
                    onValueChange={field.onChange}
                    placeholder={t('expenses.select_client')}
                    items={mockClients.map(client => ({
                      label: client.name,
                      value: client.name,
                    }))}
                  />
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='grid grid-cols-2 gap-4'>
              <FormField
                control={form.control}
                name='startDate'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('date_picker.start_date')}</FormLabel>
                    <FormControl>
                      <DatePicker
                        value={field.value ? new Date(field.value) : undefined}
                        onChange={(date) => {
                          field.onChange(date ? date.toISOString().split('T')[0] : '')
                        }}
                        placeholder={t('date_picker.select_date')}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='endDate'
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('date_picker.end_date')}</FormLabel>
                    <FormControl>
                      <DatePicker
                        value={field.value ? new Date(field.value) : undefined}
                        onChange={(date) => {
                          field.onChange(date ? date.toISOString().split('T')[0] : '')
                        }}
                        placeholder={t('date_picker.select_date')}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name='description'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('expenses.description_optional')}</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={t('expenses.description_placeholder')}
                      className='resize-none'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>

        <SheetFooter className='gap-2'>
          <SheetClose asChild>
            <Button variant='outline'>{t('buttons.cancel')}</Button>
          </SheetClose>
          <Button form='expense-item-form' type='submit'>
            {isUpdate ? t('expenses.update_item') : t('expenses.create_item')}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  )
}
