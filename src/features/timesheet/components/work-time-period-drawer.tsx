import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { DatePickerWithRange } from '@/components/ui/date-picker-with-range'
import { Separator } from '@/components/ui/separator'
import { workTimePeriodFormSchema, type WorkTimePeriodForm } from '../data/schema'
import { useTimesheet } from '../context/timesheet-context'
import {
  useCreateWorkTimePeriod,
  useUpdateWorkTimePeriod,
  useConsultantInfo,
  useClients
} from '../hooks/use-timesheet'
import { IconLoader2, IconPlus, IconTrash } from '@tabler/icons-react'
import { useEffect } from 'react'
import { useCommonTranslations } from '@/context/i18n-context'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function WorkTimePeriodDrawer({ open, onOpenChange }: Props) {
  const { currentPeriod } = useTimesheet()
  const { t } = useCommonTranslations()
  const isEditing = !!currentPeriod

  // React Query hooks
  const { data: consultantInfo } = useConsultantInfo()
  const { data: clients = [] } = useClients()
  const createMutation = useCreateWorkTimePeriod()
  const updateMutation = useUpdateWorkTimePeriod()

  const form = useForm<WorkTimePeriodForm>({
    resolver: zodResolver(workTimePeriodFormSchema),
    defaultValues: {
      startDate: new Date(),
      endDate: new Date(),
      notes: '',
      activities: [
        {
          activityName: '',
          hours: '',
          description: '',
          isBillable: true,
          clientId: undefined,
          location: 'remote' as const,
        },
      ],
    },
  })

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'activities',
  })

  // Set form values when editing
  useEffect(() => {
    if (currentPeriod && isEditing) {
      form.reset({
        startDate: new Date(currentPeriod.startDate),
        endDate: new Date(currentPeriod.endDate),
        notes: currentPeriod.notes || '',
        activities: currentPeriod.activities.map(activity => ({
          activityName: activity.activityName,
          hours: activity.hours.toString(),
          description: activity.description || '',
          isBillable: activity.isBillable,
          clientId: activity.client?.id,
          location: (activity as any).location || 'remote', // Default to remote if not set
        })),
      })
    }
  }, [currentPeriod, isEditing, form])

  const onSubmit = (data: WorkTimePeriodForm) => {
    if (!consultantInfo) {
      return // Error handling is done by the hook
    }

    const totalHours = data.activities.reduce((sum, activity) => sum + parseFloat(activity.hours), 0)

    // Calculate remote work percentage automatically
    const remoteHours = data.activities
      .filter(activity => activity.location === 'remote')
      .reduce((sum, activity) => sum + parseFloat(activity.hours), 0)
    const remoteWorkPercentage = totalHours > 0 ? Math.round((remoteHours / totalHours) * 100) : 0

    const periodData = {
      consultantId: parseInt(consultantInfo.id),
      startDate: data.startDate.toISOString().split('T')[0],
      endDate: data.endDate.toISOString().split('T')[0],
      totalHours,
      remoteWorkPercentage,
      notes: data.notes,
      activities: data.activities.map(activity => ({
        activityName: activity.activityName,
        hours: parseFloat(activity.hours),
        description: activity.description,
        isBillable: activity.isBillable,
        clientId: activity.clientId,
        location: activity.location,
      })),
    }

    if (isEditing && currentPeriod) {
      updateMutation.mutate({
        id: currentPeriod.id,
        ...periodData,
      }, {
        onSuccess: () => {
          onOpenChange(false)
          form.reset()
        }
      })
    } else {
      createMutation.mutate(periodData, {
        onSuccess: () => {
          onOpenChange(false)
          form.reset()
        }
      })
    }
  }

  const addActivity = () => {
    append({
      activityName: '',
      hours: '',
      description: '',
      isBillable: true,
      clientId: undefined,
      location: 'remote' as const,
    })
  }

  const removeActivity = (index: number) => {
    if (fields.length > 1) {
      remove(index)
    }
  }

  const isLoading = createMutation.isPending || updateMutation.isPending

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className='w-[700px] sm:w-[800px] max-w-[90vw] overflow-y-auto'>
        <SheetHeader>
          <SheetTitle>
            {isEditing ? t('timesheet.edit_work_period') : t('timesheet.add_work_period')}
          </SheetTitle>
          <SheetDescription>
            {isEditing
              ? t('timesheet.update_work_period_description')
              : t('timesheet.add_work_period_description')}
          </SheetDescription>
        </SheetHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6 mt-6'>
            {/* Period Details */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">{t('timesheet.period_details')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name='startDate'
                  render={({ field: startField }) => (
                    <FormField
                      control={form.control}
                      name='endDate'
                      render={({ field: endField }) => (
                        <FormItem>
                          <FormLabel>{t('date_picker.work_period_dates')}</FormLabel>
                          <FormControl>
                            <DatePickerWithRange
                              value={{
                                from: startField.value,
                                to: endField.value,
                              }}
                              onChange={(dateRange) => {
                                if (dateRange?.from) {
                                  startField.onChange(dateRange.from)
                                }
                                if (dateRange?.to) {
                                  endField.onChange(dateRange.to)
                                } else if (dateRange?.from) {
                                  // If only start date is selected, set end date to same date
                                  endField.onChange(dateRange.from)
                                }
                              }}
                              placeholder={t('date_picker.select_date_range')}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                />

                <FormField
                  control={form.control}
                  name='notes'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('timesheet.notes_optional')}</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder={t('timesheet.notes_placeholder')}
                          className='resize-none'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Activities */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-lg">{t('timesheet.activities')}</CardTitle>
                <Button type="button" variant="outline" size="sm" onClick={addActivity}>
                  <IconPlus className="h-4 w-4 mr-2" />
                  {t('timesheet.add_activity')}
                </Button>
              </CardHeader>
              <CardContent className="space-y-4">
                {fields.map((field, index) => (
                  <div key={field.id} className="space-y-4 p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{t('timesheet.activity')} {index + 1}</h4>
                      {fields.length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeActivity(index)}
                        >
                          <IconTrash className="h-4 w-4" />
                        </Button>
                      )}
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name={`activities.${index}.activityName`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('timesheet.activity_name')}</FormLabel>
                            <FormControl>
                              <Input placeholder={t('timesheet.activity_placeholder')} {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`activities.${index}.hours`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('timesheet.hours')}</FormLabel>
                            <FormControl>
                              <Input
                                type='number'
                                step='0.5'
                                min='0'
                                placeholder={t('timesheet.hours_placeholder')}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name={`activities.${index}.clientId`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('timesheet.client_optional')}</FormLabel>
                            <Select onValueChange={(value) => field.onChange(value ? parseInt(value) : undefined)} value={field.value?.toString()}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder={t('timesheet.select_client')} />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {clients.map((client) => (
                                  <SelectItem key={client.id} value={client.id.toString()}>
                                    {client.name} ({client.code})
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`activities.${index}.location`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('timesheet.work_location')}</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder={t('timesheet.select_location')} />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="remote">{t('timesheet.remote')}</SelectItem>
                                <SelectItem value="onsite">{t('timesheet.onsite')}</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name={`activities.${index}.description`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('timesheet.description_optional')}</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder={t('timesheet.description_placeholder')}
                              className='resize-none'
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                ))}
              </CardContent>
            </Card>

            <Separator />

            <div className='flex justify-end space-x-2 pt-4'>
              <Button
                type='button'
                variant='outline'
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                {t('buttons.cancel')}
              </Button>
              <Button type='submit' disabled={isLoading}>
                {isLoading && (
                  <IconLoader2 className='mr-2 h-4 w-4 animate-spin' />
                )}
                {isEditing ? t('timesheet.update_period') : t('timesheet.create_period')}
              </Button>
            </div>
          </form>
        </Form>
      </SheetContent>
    </Sheet>
  )
}
