import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { LanguageSwitcher } from '@/components/language-switcher'
import { LanguageStatus } from '@/components/language-status'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useTranslations } from '@/context/i18n-context'
import { WorkTimeChart } from './components/work-time-chart'
import { LeavesOverview } from './components/leaves-overview'
import { ExpensesOverview } from './components/expenses-overview'
import { WorkTimeStats } from './components/work-time-stats'

export default function MyDashboard() {
  const { t } = useTranslations('mydashboard')

  return (
    <>
      {/* ===== Top Heading ===== */}
      <Header>
        <div className='ml-auto flex items-center space-x-4'>
          <Search />
          <LanguageSwitcher />
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      {/* ===== Main ===== */}
      <Main>
        <div className='mb-6 flex items-center justify-between space-y-2'>
          <div>
            <h1 className='text-2xl font-bold tracking-tight'>{t('title')}</h1>
            <p className='text-muted-foreground'>
              {t('personal_overview')}
            </p>
          </div>
        </div>

        <div className='space-y-6'>
          {/* Work Time Section */}
          <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>
            <Card className='md:col-span-1'>
              <CardHeader>
                <CardTitle>{t('work_time.distribution')}</CardTitle>
                <CardDescription>
                  {t('work_time.distribution_description')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <WorkTimeChart />
              </CardContent>
            </Card>

            <Card className='md:col-span-1 lg:col-span-2'>
              <CardHeader>
                <CardTitle>{t('work_time.statistics')}</CardTitle>
                <CardDescription>
                  {t('work_time.statistics_description')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <WorkTimeStats />
              </CardContent>
            </Card>
          </div>

          {/* Leaves and Expenses Section */}
          <div className='grid gap-6 md:grid-cols-2'>
            <Card>
              <CardHeader>
                <CardTitle>{t('leaves.overview')}</CardTitle>
                <CardDescription>
                  {t('leaves.overview_description')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <LeavesOverview />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>{t('expenses.overview')}</CardTitle>
                <CardDescription>
                  {t('expenses.overview_description')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ExpensesOverview />
              </CardContent>
            </Card>
          </div>
        </div>
      </Main>

      <LanguageStatus />
    </>
  )
}
